<template>
  <BaseModal
    :show="dialog"
    :title="title"
    :color="options.color"
    :width="options.width"
    :z-index="options.zIndex"
    :close-on-overlay="false"
    :closable="false"
    @close="cancel"
  >
    <template #content>
      <div v-if="message" class="dialog-message">
        {{ message }}
      </div>
    </template>

    <template #footer>
      <div class="dialog-actions">
        <button
          @click="cancel"
          class="dialog-btn dialog-btn--cancel"
          :class="{ 'dialog-btn--cancel--dark': $store.state.darkMode }"
        >
          Cancel
        </button>
        <button
          @click="agree"
          class="dialog-btn dialog-btn--primary"
          :class="{ 'dialog-btn--primary--dark': $store.state.darkMode }"
        >
          Yes
        </button>
      </div>
    </template>
  </BaseModal>
</template>

<script>
import { mapActions } from "vuex";
import BaseModal from "./BaseModal.vue";

export default {
  components: {
    BaseModal
  },
  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    message: null,
    title: null,
    options: {
      color: "primary",
      width: 290,
      zIndex: 1000000000,
    },
  }),
  methods: {
    ...mapActions("app", ["loadVApp","unLoadVApp"]),
    open(title, message, options=null) {
      this.loadVApp()
      this.dialog = true;
      this.title = title;
      this.message = message;
      this.options = Object.assign(this.options, options||this.options);
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      })
    },
    agree() {
      this.resolve(true);
      this.dialog = false;
      this.unLoadVApp()
    },
    cancel() {
      this.resolve(false);
      this.dialog = false;
      this.unLoadVApp()
    },
  },
};
</script>

<style scoped>
.dialog-message {
  padding: 16px 0;
  font-size: 16px;
  color: #495057;
  line-height: 1.5;
  text-align: center;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.dialog-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.dialog-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dialog-btn--cancel {
  background: #6c757d;
  color: white;
}

.dialog-btn--cancel:hover {
  background: #5a6268;
}

.dialog-btn--cancel--dark {
  background: #4a5568;
  color: #e2e8f0;
}

.dialog-btn--cancel--dark:hover {
  background: #2d3748;
}

.dialog-btn--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.dialog-btn--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.dialog-btn--primary--dark {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

.dialog-btn--primary--dark:hover {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* Dark theme message */
.dialog-message {
  transition: color 0.3s ease;
}

:deep(.base-content--dark) .dialog-message {
  color: #e2e8f0;
}

@media (max-width: 480px) {
  .dialog-actions {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-btn {
    width: 100%;
  }
}
</style>
